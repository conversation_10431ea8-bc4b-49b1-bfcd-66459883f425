package com.example.onelinediary.components

import android.media.MediaPlayer
import android.net.Uri
import android.util.Log
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.VideoView
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.rememberAsyncImagePainter
import coil.request.ImageRequest
import com.example.onelinediary.ui.theme.ButtonGreen
import com.example.onelinediary.ui.theme.TextBrown
import kotlinx.coroutines.launch
import java.io.File

/**
 * Component for displaying and playing videos
 */
@Composable
fun VideoPlayerComponent(
    videoUri: Uri,
    modifier: Modifier = Modifier
) {
    var isPlaying by remember { mutableStateOf(false) }
    var showFullScreen by remember { mutableStateOf(false) }
    val context = LocalContext.current

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxWidth()
    ) {
        // Video preview using VideoView
        AndroidView(
            factory = { ctx ->
                VideoView(ctx).apply {
                    layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                }
            },
            update = { videoView ->
                // Set error listener first to catch any errors
                videoView.setOnErrorListener { _, what, extra ->
                    Log.e("VideoPlayer", "VideoView error: what=$what, extra=$extra")
                    true // Error handled
                }

                // Set video URI
                videoView.setVideoURI(videoUri)

                // Set up prepared listener
                videoView.setOnPreparedListener { mp ->
                    mp.isLooping = false
                    mp.setOnCompletionListener {
                        isPlaying = false
                    }
                }

                // Control playback
                if (isPlaying) {
                    videoView.start()
                } else {
                    videoView.pause()
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp)
                .clip(RectangleShape)
                .border(1.dp, MaterialTheme.colorScheme.outline)
                .clickable { showFullScreen = true }
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Play/Pause button
        OutlinedButton(
            onClick = { isPlaying = !isPlaying },
            modifier = Modifier,
            contentPadding = PaddingValues(8.dp),
            colors = ButtonDefaults.outlinedButtonColors(
                containerColor = MaterialTheme.colorScheme.primary,
                contentColor = MaterialTheme.colorScheme.onPrimary
            ),
            shape = RoundedCornerShape(12.dp)
        ) {
            if (!isPlaying) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Play",
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
            Spacer(modifier = Modifier.width(4.dp))
            Text(if (isPlaying) "Pause" else "Play", color = MaterialTheme.colorScheme.onPrimary)
        }
    }

    // Full screen dialog when video is clicked
    if (showFullScreen) {
        FullScreenVideoDialog(
            videoUri = videoUri,
            onDismiss = { showFullScreen = false }
        )
    }
}

/**
 * Dialog for displaying a video in full screen
 */
@Composable
fun FullScreenVideoDialog(
    videoUri: Uri,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }

    // Add logging to track when the dialog is shown and dismissed
    LaunchedEffect(Unit) {
        Log.d("VideoPlayer", "FullScreenVideoDialog opened")
    }

    DisposableEffect(Unit) {
        onDispose {
            Log.d("VideoPlayer", "FullScreenVideoDialog disposed")
        }
    }

    // Handle back button press
    BackHandler {
        Log.d("VideoPlayer", "Back button pressed")
        onDismiss()
    }

    Dialog(
        onDismissRequest = {
            Log.d("VideoPlayer", "Dialog dismiss requested")
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.9f))
        ) {
            // Close button
            IconButton(
                onClick = {
                    Log.d("VideoPlayer", "Close button clicked")
                    onDismiss()
                },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .size(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(12.dp)
                    )
                    .border(
                        width = 1.5.dp,
                        color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f),
                        shape = RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier.size(24.dp)
                )
            }

            // Full screen video
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 70.dp, bottom = 16.dp, start = 16.dp, end = 16.dp)
            ) {
                // Video player
                AndroidView(
                    factory = { ctx ->
                        VideoView(ctx).apply {
                            layoutParams = FrameLayout.LayoutParams(
                                ViewGroup.LayoutParams.MATCH_PARENT,
                                ViewGroup.LayoutParams.MATCH_PARENT
                            )
                        }
                    },
                    update = { videoView ->
                        // Set error listener first to catch any errors
                        videoView.setOnErrorListener { _, what, extra ->
                            Log.e("VideoPlayer", "VideoView error: what=$what, extra=$extra")
                            true // Error handled
                        }

                        // Set video URI
                        videoView.setVideoURI(videoUri)

                        // Set up prepared listener
                        videoView.setOnPreparedListener { mp ->
                            mp.isLooping = false
                            mp.setOnCompletionListener {
                                isPlaying = false
                            }
                        }

                        // Control playback
                        if (isPlaying) {
                            videoView.start()
                        } else {
                            videoView.pause()
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .clip(RectangleShape)
                        .border(1.dp, MaterialTheme.colorScheme.outline)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Play/Pause button
                OutlinedButton(
                    onClick = { isPlaying = !isPlaying },
                    modifier = Modifier,
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (!isPlaying) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Play",
                            tint = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(if (isPlaying) "Pause" else "Play", color = MaterialTheme.colorScheme.onPrimary)
                }
            }
        }
    }
}

/**
 * Component for playing audio files
 */
@Composable
fun AudioPlayerComponent(
    audioUri: Uri,
    modifier: Modifier = Modifier
) {
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer by remember { mutableStateOf<MediaPlayer?>(null) }
    var showFullScreen by remember { mutableStateOf(false) }
    val context = LocalContext.current
    // Add coroutineScope for launching coroutines from composable functions
    val coroutineScope = rememberCoroutineScope()

    // Clean up media player when leaving the screen
    DisposableEffect(Unit) {
        onDispose {
            mediaPlayer?.release()
            mediaPlayer = null
        }
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxWidth()
    ) {
        // Audio player UI
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp)
                .clickable { showFullScreen = true },
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Play/Pause button
                OutlinedButton(
                    onClick = {
                        if (isPlaying) {
                            // Stop playback
                            mediaPlayer?.stop()
                            mediaPlayer?.release()
                            mediaPlayer = null
                            isPlaying = false
                        } else {
                            // Start playback using coroutine
                            coroutineScope.launch {
                                try {
                                    val player = MediaPlayer().apply {
                                        setDataSource(context, audioUri)
                                        prepare()
                                        start()
                                        setOnCompletionListener {
                                            isPlaying = false
                                            mediaPlayer = null
                                        }
                                    }
                                    mediaPlayer = player
                                    isPlaying = true
                                } catch (e: Exception) {
                                    Log.e("AudioPlayer", "Error playing audio", e)
                                }
                            }
                        }
                    },
                    modifier = Modifier,
                    contentPadding = PaddingValues(8.dp),
                    colors = ButtonDefaults.outlinedButtonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    if (!isPlaying) {
                        Icon(
                            imageVector = Icons.Default.PlayArrow,
                            contentDescription = "Play",
                            tint = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.size(24.dp)
                        )
                    } else {
                        // Just use text for stop
                        Text(
                            text = "■",  // Unicode square symbol for stop
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                    }
                }

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = if (isPlaying) "Playing audio..." else "Audio recording",
                    style = MaterialTheme.typography.bodyLarge
                )
            }
        }
    }

    // Full screen dialog when audio player is clicked
    if (showFullScreen) {
        FullScreenAudioDialog(
            audioUri = audioUri,
            onDismiss = { showFullScreen = false }
        )
    }
}

/**
 * Dialog for displaying an audio player in full screen
 */
@Composable
fun FullScreenAudioDialog(
    audioUri: Uri,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current
    var isPlaying by remember { mutableStateOf(false) }
    var mediaPlayer by remember { mutableStateOf<MediaPlayer?>(null) }
    val coroutineScope = rememberCoroutineScope()

    // Add logging to track when the dialog is shown and dismissed
    LaunchedEffect(Unit) {
        Log.d("AudioPlayer", "FullScreenAudioDialog opened")
    }

    // Clean up media player when leaving the screen
    DisposableEffect(Unit) {
        onDispose {
            Log.d("AudioPlayer", "FullScreenAudioDialog disposed")
            mediaPlayer?.release()
            mediaPlayer = null
        }
    }

    // Handle back button press
    BackHandler {
        Log.d("AudioPlayer", "Back button pressed")
        // Stop playback if it's playing
        if (isPlaying) {
            mediaPlayer?.stop()
            mediaPlayer?.release()
            mediaPlayer = null
        }
        onDismiss()
    }

    Dialog(
        onDismissRequest = {
            Log.d("AudioPlayer", "Dialog dismiss requested")
            // Stop playback if it's playing
            if (isPlaying) {
                mediaPlayer?.stop()
                mediaPlayer?.release()
                mediaPlayer = null
            }
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.9f))
        ) {
            // Close button
            IconButton(
                onClick = {
                    Log.d("AudioPlayer", "Close button clicked")
                    // Stop playback if it's playing
                    if (isPlaying) {
                        mediaPlayer?.stop()
                        mediaPlayer?.release()
                        mediaPlayer = null
                    }
                    onDismiss()
                },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .size(48.dp)
                    .background(
                        color = ButtonGreen,
                        shape = RoundedCornerShape(12.dp)
                    )
                    .border(
                        width = 1.5.dp,
                        color = TextBrown.copy(alpha = 0.7f),
                        shape = RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = TextBrown,
                    modifier = Modifier.size(24.dp)
                )
            }

            // Full screen audio player
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 70.dp, bottom = 16.dp, start = 16.dp, end = 16.dp)
            ) {
                // Audio player UI
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp)
                    ) {
                        Text(
                            text = "Audio Player",
                            style = MaterialTheme.typography.headlineMedium,
                            modifier = Modifier.padding(bottom = 24.dp)
                        )

                        // Play/Pause button (larger in fullscreen)
                        OutlinedButton(
                            onClick = {
                                if (isPlaying) {
                                    // Stop playback
                                    mediaPlayer?.stop()
                                    mediaPlayer?.release()
                                    mediaPlayer = null
                                    isPlaying = false
                                } else {
                                    // Start playback using coroutine
                                    coroutineScope.launch {
                                        try {
                                            val player = MediaPlayer().apply {
                                                setDataSource(context, audioUri)
                                                prepare()
                                                start()
                                                setOnCompletionListener {
                                                    isPlaying = false
                                                    mediaPlayer = null
                                                }
                                            }
                                            mediaPlayer = player
                                            isPlaying = true
                                        } catch (e: Exception) {
                                            Log.e("AudioPlayer", "Error playing audio", e)
                                        }
                                    }
                                }
                            },
                            modifier = Modifier.size(80.dp),
                            contentPadding = PaddingValues(8.dp),
                            colors = ButtonDefaults.outlinedButtonColors(
                                containerColor = MaterialTheme.colorScheme.primary,
                                contentColor = MaterialTheme.colorScheme.onPrimary
                            ),
                            shape = RoundedCornerShape(40.dp)
                        ) {
                            if (!isPlaying) {
                                Icon(
                                    imageVector = Icons.Default.PlayArrow,
                                    contentDescription = "Play",
                                    tint = MaterialTheme.colorScheme.onPrimary,
                                    modifier = Modifier.size(40.dp)
                                )
                            } else {
                                // Just use text for stop
                                Text(
                                    text = "■",  // Unicode square symbol for stop
                                    style = MaterialTheme.typography.headlineLarge,
                                    color = MaterialTheme.colorScheme.onPrimary
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                        Text(
                            text = if (isPlaying) "Playing audio..." else "Press play to listen",
                            style = MaterialTheme.typography.bodyLarge
                        )
                    }
                }
            }
        }
    }
}

/**
 * Component for displaying images with zoom capability
 */
@Composable
fun ImageViewerComponent(
    imageUri: Uri,
    modifier: Modifier = Modifier,
    contentDescription: String? = null
) {
    val context = LocalContext.current
    var showFullScreen by remember { mutableStateOf(false) }

    // Regular image display
    Image(
        painter = rememberAsyncImagePainter(
            ImageRequest.Builder(context)
                .data(imageUri)
                .build()
        ),
        contentDescription = contentDescription ?: "Image",
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp)
            .clip(RectangleShape)
            .border(1.dp, MaterialTheme.colorScheme.outline)
            .clickable { showFullScreen = true },
        contentScale = ContentScale.Fit
    )

    // Full screen dialog when image is clicked
    if (showFullScreen) {
        FullScreenImageDialog(
            imageUri = imageUri,
            onDismiss = { showFullScreen = false }
        )
    }
}

/**
 * Dialog for displaying an image in full screen with zoom and pan capabilities
 */
@Composable
fun FullScreenImageDialog(
    imageUri: Uri,
    onDismiss: () -> Unit
) {
    val context = LocalContext.current

    // Add logging to track when the dialog is shown and dismissed
    LaunchedEffect(Unit) {
        Log.d("ImageViewer", "FullScreenImageDialog opened")
    }

    DisposableEffect(Unit) {
        onDispose {
            Log.d("ImageViewer", "FullScreenImageDialog disposed")
        }
    }

    // State for zoom and pan
    var scale by remember { mutableStateOf(1f) }
    var offsetX by remember { mutableStateOf(0f) }
    var offsetY by remember { mutableStateOf(0f) }

    // Handle back button press
    BackHandler {
        Log.d("ImageViewer", "Back button pressed")
        onDismiss()
    }

    Dialog(
        onDismissRequest = {
            Log.d("ImageViewer", "Dialog dismiss requested")
            onDismiss()
        },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = true,
            usePlatformDefaultWidth = false
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.9f))
        ) {
            // Close button - improved with better click handling
            IconButton(
                onClick = {
                    Log.d("ImageViewer", "Close button clicked")
                    onDismiss()
                },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .size(48.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(12.dp)
                    )
                    .border(
                        width = 1.5.dp,
                        color = MaterialTheme.colorScheme.onPrimary.copy(alpha = 0.7f),
                        shape = RoundedCornerShape(12.dp)
                    )
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Close",
                    tint = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier.size(24.dp)
                )
            }

            // Zoomable image - with padding at the top to avoid overlapping with close button
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 70.dp) // Add padding to avoid overlapping with close button
            ) {
                Image(
                    painter = rememberAsyncImagePainter(
                        ImageRequest.Builder(context)
                            .data(imageUri)
                            .build()
                    ),
                    contentDescription = "Full screen image",
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectTransformGestures { _, pan, zoom, _ ->
                                scale = (scale * zoom).coerceIn(1f, 3f)

                                // Only apply pan if zoomed in
                                if (scale > 1f) {
                                    offsetX += pan.x
                                    offsetY += pan.y

                                    // Limit pan based on zoom level
                                    val maxOffset = (scale - 1) * 500 // Arbitrary limit
                                    offsetX = offsetX.coerceIn(-maxOffset, maxOffset)
                                    offsetY = offsetY.coerceIn(-maxOffset, maxOffset)
                                } else {
                                    // Reset offset when zoomed out
                                    offsetX = 0f
                                    offsetY = 0f
                                }
                            }
                        }
                        .graphicsLayer(
                            scaleX = scale,
                            scaleY = scale,
                            translationX = offsetX,
                            translationY = offsetY
                        ),
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}
